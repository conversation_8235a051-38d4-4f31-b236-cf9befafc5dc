"use client";

import React from "react";
import { 
  BarChart3, 
  CreditCard, 
  Package, 
  FileText, 
  Cloud, 
  Shield,
  Smartphone,
  Users,
  TrendingUp,
  Zap
} from "lucide-react";

interface Feature {
  icon: React.ReactNode;
  title: string;
  description: string;
  highlight?: boolean;
}

export const FeaturesSection: React.FC = () => {
  const problemPoints = [
    "Kesulitan melacak penjualan dan inventaris",
    "Proses pembayaran yang rumit dan lambat",
    "Laporan keuangan yang tidak akurat",
    "Tidak ada insight bisnis real-time"
  ];

  const mainFeatures: Feature[] = [
    {
      icon: <BarChart3 className="w-8 h-8" />,
      title: "Analitik Real-time",
      description: "Dashboard komprehensif dengan insight bisnis mendalam, laporan penjualan otomatis, dan prediksi tren untuk pengambilan keputusan yang lebih baik.",
      highlight: true
    },
    {
      icon: <CreditCard className="w-8 h-8" />,
      title: "Multiple Payment Gateway",
      description: "Integrasi dengan Midtrans, Xendit, dan payment gateway populer lainnya. Terima pembayaran dari berbagai metode dengan aman dan mudah.",
      highlight: true
    },
    {
      icon: <Package className="w-8 h-8" />,
      title: "Manajemen Inventaris",
      description: "Kelola stok produk, tracking otomatis, notifikasi stok menipis, dan manajemen supplier dalam satu platform terintegrasi.",
      highlight: true
    },
    {
      icon: <FileText className="w-8 h-8" />,
      title: "Laporan Komprehensif",
      description: "Generate laporan penjualan, keuangan, dan performa bisnis dengan format yang mudah dipahami dan dapat diekspor ke berbagai format."
    }
  ];

  const additionalFeatures: Feature[] = [
    {
      icon: <Cloud className="w-6 h-6" />,
      title: "Berbasis Cloud",
      description: "Akses data dari mana saja, backup otomatis, dan sinkronisasi real-time"
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: "Keamanan Tinggi",
      description: "Enkripsi end-to-end, compliance dengan standar PCI DSS"
    },
    {
      icon: <Smartphone className="w-6 h-6" />,
      title: "Mobile Responsive",
      description: "Interface yang optimal di desktop, tablet, dan smartphone"
    },
    {
      icon: <Users className="w-6 h-6" />,
      title: "Multi-user Support",
      description: "Kelola tim dengan role dan permission yang fleksibel"
    },
    {
      icon: <TrendingUp className="w-6 h-6" />,
      title: "Growth Analytics",
      description: "Insight pertumbuhan bisnis dan rekomendasi strategis"
    },
    {
      icon: <Zap className="w-6 h-6" />,
      title: "Fast Performance",
      description: "Loading cepat dan performa optimal untuk produktivitas maksimal"
    }
  ];

  return (
    <section id="features" className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Problem Statement */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Tantangan yang Dihadapi Bisnis Modern
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Banyak bisnis masih menggunakan sistem manual atau tools terpisah yang tidak terintegrasi
          </p>
          
          <div className="grid md:grid-cols-2 gap-4 max-w-4xl mx-auto">
            {problemPoints.map((problem, index) => (
              <div key={index} className="flex items-center text-left bg-red-50 p-4 rounded-lg border border-red-200">
                <div className="w-2 h-2 bg-red-500 rounded-full mr-3 flex-shrink-0" />
                <span className="text-gray-700">{problem}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Solution Overview */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-blue-100 text-blue-800 text-sm font-medium mb-6">
            Solusi KivaPOS
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Platform All-in-One untuk Bisnis yang Berkembang
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            KivaPOS menggabungkan semua kebutuhan bisnis Anda dalam satu platform yang powerful, 
            mudah digunakan, dan dapat diandalkan.
          </p>
        </div>

        {/* Main Features */}
        <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-8 mb-16">
          {mainFeatures.map((feature, index) => (
            <div 
              key={index} 
              className={`relative p-8 rounded-2xl transition-all duration-300 hover:shadow-xl hover:-translate-y-1 ${
                feature.highlight 
                  ? 'bg-white border-2 border-blue-200 shadow-lg' 
                  : 'bg-white border border-gray-200 shadow-md'
              }`}
            >
              {feature.highlight && (
                <div className="absolute -top-3 left-6 bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                  Popular
                </div>
              )}
              
              <div className={`inline-flex p-3 rounded-xl mb-4 ${
                feature.highlight ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'
              }`}>
                {feature.icon}
              </div>
              
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                {feature.title}
              </h3>
              
              <p className="text-gray-600 leading-relaxed">
                {feature.description}
              </p>
            </div>
          ))}
        </div>

        {/* Additional Features Grid */}
        <div className="bg-white rounded-2xl p-8 shadow-lg">
          <h3 className="text-2xl font-bold text-gray-900 text-center mb-8">
            Fitur Lengkap untuk Semua Kebutuhan Bisnis
          </h3>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {additionalFeatures.map((feature, index) => (
              <div key={index} className="flex items-start space-x-4 p-4 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex-shrink-0 p-2 bg-blue-100 text-blue-600 rounded-lg">
                  {feature.icon}
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">
                    {feature.title}
                  </h4>
                  <p className="text-sm text-gray-600">
                    {feature.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};
