"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight, Play, CheckCircle } from "lucide-react";

export const HeroSection: React.FC = () => {
  const benefits = [
    "Gratis 30 hari untuk pengguna baru",
    "Setup dalam 5 menit",
    "Dukungan 24/7",
  ];

  return (
    <section className="relative min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))]" />
      
      {/* Gradient Overlays */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 via-transparent to-purple-600/20" />
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-16">
        <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center min-h-[calc(100vh-5rem)]">
          {/* Left Column - Content */}
          <div className="text-center lg:text-left">
            {/* Badge */}
            <div className="inline-flex items-center px-4 py-2 rounded-full bg-blue-600/10 border border-blue-500/20 text-blue-400 text-sm font-medium mb-6">
              <span className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse" />
              Solusi POS Terdepan di Indonesia
            </div>

            {/* Main Headline */}
            <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold text-white mb-6 leading-tight">
              Kelola Bisnis Anda dengan{" "}
              <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                KivaPOS
              </span>
            </h1>

            {/* Subheadline */}
            <p className="text-xl lg:text-2xl text-gray-300 mb-8 leading-relaxed max-w-2xl">
              Platform manajemen pembayaran all-in-one yang membantu bisnis Anda 
              berkembang dengan analitik real-time, integrasi payment gateway, 
              dan manajemen inventaris yang powerful.
            </p>

            {/* Benefits List */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-center lg:justify-start gap-4 mb-8">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-center text-gray-300">
                  <CheckCircle className="w-5 h-5 text-green-400 mr-2 flex-shrink-0" />
                  <span className="text-sm">{benefit}</span>
                </div>
              ))}
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-8">
              <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg">
                <Link href="/register" className="flex items-center">
                  Mulai Gratis Sekarang
                  <ArrowRight className="ml-2 w-5 h-5" />
                </Link>
              </Button>
              
              <Button 
                asChild 
                variant="outline" 
                size="lg" 
                className="border-white/20 text-white hover:bg-white/10 px-8 py-4 text-lg"
              >
                <Link href="#demo" className="flex items-center">
                  <Play className="mr-2 w-5 h-5" />
                  Lihat Demo
                </Link>
              </Button>
            </div>

            {/* Trust Indicators */}
            <div className="text-center lg:text-left">
              <p className="text-gray-400 text-sm mb-4">Dipercaya oleh 10,000+ bisnis di Indonesia</p>
              <div className="flex items-center justify-center lg:justify-start space-x-6 opacity-60">
                <div className="text-white font-semibold">Tokopedia</div>
                <div className="text-white font-semibold">Shopee</div>
                <div className="text-white font-semibold">Blibli</div>
                <div className="text-white font-semibold">Bukalapak</div>
              </div>
            </div>
          </div>

          {/* Right Column - Hero Image/Dashboard Preview */}
          <div className="relative">
            {/* Main Dashboard Image */}
            <div className="relative rounded-2xl overflow-hidden shadow-2xl border border-white/10 bg-gradient-to-br from-white/5 to-white/10 backdrop-blur-sm">
              <div className="aspect-[4/3] relative">
                <Image
                  src="/dashboard-preview.png"
                  alt="KivaPOS Dashboard Preview"
                  fill
                  className="object-cover"
                  priority
                  onError={(e) => {
                    // Fallback if image doesn't exist
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                  }}
                />
                {/* Fallback content if image doesn't load */}
                <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-blue-600/20 to-purple-600/20">
                  <div className="text-center text-white">
                    <div className="w-24 h-24 mx-auto mb-4 bg-white/10 rounded-2xl flex items-center justify-center">
                      <div className="w-12 h-12 bg-blue-500 rounded-lg" />
                    </div>
                    <h3 className="text-xl font-semibold mb-2">Dashboard KivaPOS</h3>
                    <p className="text-gray-300">Interface yang intuitif dan powerful</p>
                  </div>
                </div>
              </div>
              
              {/* Floating Elements */}
              <div className="absolute -top-4 -right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg">
                <div className="text-sm font-semibold">+127% Growth</div>
              </div>
              
              <div className="absolute -bottom-4 -left-4 bg-white text-gray-900 px-4 py-2 rounded-lg shadow-lg">
                <div className="text-sm font-semibold">Real-time Analytics</div>
              </div>
            </div>

            {/* Background Decorations */}
            <div className="absolute -top-8 -right-8 w-32 h-32 bg-blue-500/20 rounded-full blur-2xl" />
            <div className="absolute -bottom-8 -left-8 w-40 h-40 bg-purple-500/20 rounded-full blur-2xl" />
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2">
        <div className="animate-bounce">
          <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse" />
          </div>
        </div>
      </div>
    </section>
  );
};
