"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";
import { 
  Mail, 
  Phone, 
  MapPin, 
  Facebook, 
  Twitter, 
  Instagram, 
  Linkedin,
  Youtube,
  ArrowRight
} from "lucide-react";

interface FooterLink {
  label: string;
  href: string;
}

interface FooterSection {
  title: string;
  links: FooterLink[];
}

export const FooterSection: React.FC = () => {
  const footerSections: FooterSection[] = [
    {
      title: "Produk",
      links: [
        { label: "Fitur", href: "#features" },
        { label: "Harga", href: "#pricing" },
        { label: "Demo", href: "/demo" },
        { label: "API", href: "/api-docs" },
        { label: "Integrasi", href: "/integrations" }
      ]
    },
    {
      title: "Perusahaan",
      links: [
        { label: "Tentang Kami", href: "/about" },
        { label: "<PERSON>ri<PERSON>", href: "/careers" },
        { label: "Blog", href: "/blog" },
        { label: "Press Kit", href: "/press" },
        { label: "Partner", href: "/partners" }
      ]
    },
    {
      title: "Dukungan",
      links: [
        { label: "Pusat Bantuan", href: "/help" },
        { label: "Dokumentasi", href: "/docs" },
        { label: "Status Sistem", href: "/status" },
        { label: "Hubungi Kami", href: "/contact" },
        { label: "Pelatihan", href: "/training" }
      ]
    },
    {
      title: "Legal",
      links: [
        { label: "Kebijakan Privasi", href: "/privacy" },
        { label: "Syarat & Ketentuan", href: "/terms" },
        { label: "Kebijakan Cookie", href: "/cookies" },
        { label: "Keamanan", href: "/security" },
        { label: "Compliance", href: "/compliance" }
      ]
    }
  ];

  const socialLinks = [
    { icon: <Facebook className="w-5 h-5" />, href: "https://facebook.com/kivapos", label: "Facebook" },
    { icon: <Twitter className="w-5 h-5" />, href: "https://twitter.com/kivapos", label: "Twitter" },
    { icon: <Instagram className="w-5 h-5" />, href: "https://instagram.com/kivapos", label: "Instagram" },
    { icon: <Linkedin className="w-5 h-5" />, href: "https://linkedin.com/company/kivapos", label: "LinkedIn" },
    { icon: <Youtube className="w-5 h-5" />, href: "https://youtube.com/kivapos", label: "YouTube" }
  ];

  const contactInfo = [
    {
      icon: <Mail className="w-5 h-5" />,
      label: "Email",
      value: "<EMAIL>",
      href: "mailto:<EMAIL>"
    },
    {
      icon: <Phone className="w-5 h-5" />,
      label: "Telepon",
      value: "+62 21 1234 5678",
      href: "tel:+622112345678"
    },
    {
      icon: <MapPin className="w-5 h-5" />,
      label: "Alamat",
      value: "Jakarta, Indonesia",
      href: "https://maps.google.com"
    }
  ];

  return (
    <footer className="bg-gray-900 text-gray-300">
      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-2">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-2 mb-6">
              <div className="relative w-8 h-8">
                <Image
                  src="/images/kivapos-logo.png"
                  alt="KivaPOS Logo"
                  fill
                  className="object-contain"
                />
              </div>
              <span className="text-xl font-bold text-white">KivaPOS</span>
            </Link>
            
            <p className="text-gray-400 mb-6 leading-relaxed">
              Platform manajemen pembayaran all-in-one yang membantu bisnis Anda 
              berkembang dengan analitik real-time, integrasi payment gateway, 
              dan manajemen inventaris yang powerful.
            </p>

            {/* Contact Info */}
            <div className="space-y-3">
              {contactInfo.map((contact, index) => (
                <a
                  key={index}
                  href={contact.href}
                  className="flex items-center space-x-3 text-gray-400 hover:text-white transition-colors"
                >
                  <div className="text-blue-400">
                    {contact.icon}
                  </div>
                  <span className="text-sm">{contact.value}</span>
                </a>
              ))}
            </div>
          </div>

          {/* Footer Links */}
          {footerSections.map((section, index) => (
            <div key={index} className="lg:col-span-1">
              <h3 className="text-white font-semibold mb-4">
                {section.title}
              </h3>
              <ul className="space-y-3">
                {section.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <Link
                      href={link.href}
                      className="text-gray-400 hover:text-white transition-colors text-sm"
                    >
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Newsletter Signup */}
        <div className="mt-12 pt-8 border-t border-gray-800">
          <div className="max-w-md">
            <h3 className="text-white font-semibold mb-4">
              Dapatkan Update Terbaru
            </h3>
            <p className="text-gray-400 text-sm mb-4">
              Berlangganan newsletter kami untuk mendapatkan tips bisnis, update fitur, dan penawaran khusus.
            </p>
            <form className="flex gap-3">
              <input
                type="email"
                placeholder="Masukkan email Anda"
                className="flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <button
                type="submit"
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors flex items-center"
              >
                <ArrowRight className="w-4 h-4" />
              </button>
            </form>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            {/* Copyright */}
            <div className="text-gray-400 text-sm mb-4 md:mb-0">
              © {new Date().getFullYear()} KivaPOS. Hak cipta dilindungi undang-undang.
            </div>

            {/* Social Links */}
            <div className="flex items-center space-x-4">
              <span className="text-gray-400 text-sm mr-2">Ikuti kami:</span>
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-white transition-colors"
                  aria-label={social.label}
                >
                  {social.icon}
                </a>
              ))}
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};
