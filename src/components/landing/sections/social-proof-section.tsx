"use client";

import React from "react";
import Image from "next/image";
import { Star, Shield, Award, Users, TrendingUp, Clock } from "lucide-react";

interface Testimonial {
  name: string;
  role: string;
  company: string;
  content: string;
  rating: number;
  avatar?: string;
}

interface Statistic {
  icon: React.ReactNode;
  value: string;
  label: string;
  description: string;
}

export const SocialProofSection: React.FC = () => {
  const testimonials: Testimonial[] = [
    {
      name: "<PERSON><PERSON>",
      role: "Owner",
      company: "Warung Makan Sederhana",
      content: "KivaPOS mengubah cara saya mengelola warung. Sekarang saya bisa melihat laporan penjualan real-time dan tidak perlu lagi mencatat manual. Sangat membantu!",
      rating: 5
    },
    {
      name: "<PERSON><PERSON>",
      role: "Manager",
      company: "Toko Fashion Trendy",
      content: "Integrasi dengan payment gateway sangat mudah. Pelanggan bisa bayar dengan berbagai metode dan semua tercatat otomatis. Efisiensi meningkat drastis!",
      rating: 5
    },
    {
      name: "<PERSON>",
      role: "CEO",
      company: "Minimarket Berkah",
      content: "Fitur manajemen inventaris KivaPOS luar biasa. Stok selalu terpantau, ada notifikasi otomatis, dan laporan yang sangat detail. Recommended!",
      rating: 5
    }
  ];

  const statistics: Statistic[] = [
    {
      icon: <Users className="w-8 h-8" />,
      value: "10,000+",
      label: "Bisnis Aktif",
      description: "Dipercaya oleh ribuan bisnis di seluruh Indonesia"
    },
    {
      icon: <TrendingUp className="w-8 h-8" />,
      value: "1M+",
      label: "Transaksi Diproses",
      description: "Lebih dari 1 juta transaksi berhasil diproses setiap bulan"
    },
    {
      icon: <Clock className="w-8 h-8" />,
      value: "99.9%",
      label: "Uptime",
      description: "Sistem yang stabil dan dapat diandalkan 24/7"
    },
    {
      icon: <Award className="w-8 h-8" />,
      value: "4.9/5",
      label: "Rating Pengguna",
      description: "Kepuasan tinggi dari pengguna di seluruh Indonesia"
    }
  ];

  const partners = [
    { name: "Midtrans", logo: "/partners/midtrans.png" },
    { name: "Xendit", logo: "/partners/xendit.png" },
    { name: "Bank BCA", logo: "/partners/bca.png" },
    { name: "Bank Mandiri", logo: "/partners/mandiri.png" },
    { name: "GoPay", logo: "/partners/gopay.png" },
    { name: "OVO", logo: "/partners/ovo.png" }
  ];

  const certifications = [
    { name: "PCI DSS Compliant", icon: <Shield className="w-6 h-6" /> },
    { name: "ISO 27001", icon: <Award className="w-6 h-6" /> },
    { name: "SSL Secured", icon: <Shield className="w-6 h-6" /> }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Statistics */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Dipercaya oleh Ribuan Bisnis
          </h2>
          <p className="text-xl text-gray-600 mb-12">
            Bergabunglah dengan komunitas bisnis yang berkembang bersama KivaPOS
          </p>
          
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {statistics.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 text-blue-600 rounded-2xl mb-4">
                  {stat.icon}
                </div>
                <div className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">
                  {stat.value}
                </div>
                <div className="text-lg font-semibold text-gray-700 mb-1">
                  {stat.label}
                </div>
                <p className="text-sm text-gray-500">
                  {stat.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Testimonials */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              Apa Kata Pengguna Kami
            </h3>
            <p className="text-lg text-gray-600">
              Dengarkan pengalaman nyata dari bisnis yang telah berkembang dengan KivaPOS
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-gray-50 p-6 rounded-2xl border border-gray-200 hover:shadow-lg transition-shadow">
                {/* Rating */}
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                
                {/* Content */}
                <p className="text-gray-700 mb-6 leading-relaxed">
                  "{testimonial.content}"
                </p>
                
                {/* Author */}
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mr-4">
                    <span className="text-blue-600 font-semibold text-lg">
                      {testimonial.name.charAt(0)}
                    </span>
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900">
                      {testimonial.name}
                    </div>
                    <div className="text-sm text-gray-500">
                      {testimonial.role}, {testimonial.company}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Partners & Integrations */}
        <div className="mb-16">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Terintegrasi dengan Platform Terpercaya
            </h3>
            <p className="text-gray-600">
              KivaPOS bekerja sama dengan payment gateway dan layanan terbaik di Indonesia
            </p>
          </div>
          
          <div className="grid grid-cols-3 md:grid-cols-6 gap-8 items-center opacity-60 hover:opacity-100 transition-opacity">
            {partners.map((partner, index) => (
              <div key={index} className="flex items-center justify-center">
                <div className="text-gray-400 font-semibold text-sm">
                  {partner.name}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Security & Certifications */}
        <div className="bg-gray-50 rounded-2xl p-8 text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            Keamanan & Sertifikasi
          </h3>
          <p className="text-gray-600 mb-8">
            Data dan transaksi Anda dilindungi dengan standar keamanan internasional
          </p>
          
          <div className="flex flex-wrap justify-center items-center gap-8">
            {certifications.map((cert, index) => (
              <div key={index} className="flex items-center space-x-2 bg-white px-4 py-2 rounded-lg border border-gray-200">
                <div className="text-green-600">
                  {cert.icon}
                </div>
                <span className="text-sm font-medium text-gray-700">
                  {cert.name}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};
