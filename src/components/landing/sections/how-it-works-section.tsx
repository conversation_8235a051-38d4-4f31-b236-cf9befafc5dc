"use client";

import React from "react";
import { UserPlus, <PERSON>tings, Zap, TrendingUp, ArrowRight, CheckCircle } from "lucide-react";

interface Step {
  number: string;
  icon: React.ReactNode;
  title: string;
  description: string;
  details: string[];
  time: string;
}

export const HowItWorksSection: React.FC = () => {
  const steps: Step[] = [
    {
      number: "01",
      icon: <UserPlus className="w-8 h-8" />,
      title: "Daftar & Setup Akun",
      description: "Buat akun gratis dan setup profil bisnis Anda dalam hitungan menit",
      details: [
        "Registrasi dengan email atau nomor telepon",
        "Verifikasi akun yang mudah dan cepat",
        "Setup profil bisnis dan informasi dasar",
        "Pilih paket yang sesuai kebutuhan"
      ],
      time: "2 menit"
    },
    {
      number: "02",
      icon: <Settings className="w-8 h-8" />,
      title: "Konfigurasi Sistem",
      description: "Atur produk, payment gateway, dan preferensi bisnis sesuai kebutuhan",
      details: [
        "Import atau tambah produk dan kategori",
        "Konfigurasi payment gateway (Midtrans, Xendit)",
        "Setup pajak, diskon, dan aturan bisnis",
        "Atur role dan akses untuk tim"
      ],
      time: "10 menit"
    },
    {
      number: "03",
      icon: <Zap className="w-8 h-8" />,
      title: "Mulai Bertransaksi",
      description: "Sistem siap digunakan! Mulai proses penjualan dan terima pembayaran",
      details: [
        "Interface POS yang intuitif dan mudah",
        "Proses checkout yang cepat dan efisien",
        "Terima pembayaran dari berbagai metode",
        "Generate invoice dan receipt otomatis"
      ],
      time: "Langsung"
    },
    {
      number: "04",
      icon: <TrendingUp className="w-8 h-8" />,
      title: "Monitor & Analisis",
      description: "Pantau performa bisnis dengan dashboard dan laporan komprehensif",
      details: [
        "Dashboard real-time dengan insight mendalam",
        "Laporan penjualan, keuangan, dan inventaris",
        "Analisis tren dan prediksi bisnis",
        "Export data untuk keperluan akuntansi"
      ],
      time: "Real-time"
    }
  ];

  return (
    <section id="how-it-works" className="py-20 bg-gradient-to-br from-blue-50 to-indigo-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 rounded-full bg-blue-100 text-blue-800 text-sm font-medium mb-6">
            Cara Kerja
          </div>
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Mulai Dalam 4 Langkah Mudah
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Dari registrasi hingga bisnis berjalan, semuanya dapat diselesaikan dalam waktu kurang dari 15 menit
          </p>
        </div>

        {/* Steps */}
        <div className="relative">
          {/* Connection Line */}
          <div className="hidden lg:block absolute top-24 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-200 via-blue-300 to-blue-200" />
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-4">
            {steps.map((step, index) => (
              <div key={index} className="relative">
                {/* Step Card */}
                <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 relative z-10">
                  {/* Step Number */}
                  <div className="absolute -top-4 left-6 bg-blue-600 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold">
                    {index + 1}
                  </div>
                  
                  {/* Icon */}
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 text-blue-600 rounded-2xl mb-4 mt-2">
                    {step.icon}
                  </div>
                  
                  {/* Time Badge */}
                  <div className="inline-flex items-center px-3 py-1 rounded-full bg-green-100 text-green-800 text-xs font-medium mb-4">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    {step.time}
                  </div>
                  
                  {/* Content */}
                  <h3 className="text-xl font-bold text-gray-900 mb-3">
                    {step.title}
                  </h3>
                  
                  <p className="text-gray-600 mb-4 leading-relaxed">
                    {step.description}
                  </p>
                  
                  {/* Details */}
                  <ul className="space-y-2">
                    {step.details.map((detail, detailIndex) => (
                      <li key={detailIndex} className="flex items-start text-sm text-gray-500">
                        <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 mr-2 flex-shrink-0" />
                        {detail}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Arrow for desktop */}
                {index < steps.length - 1 && (
                  <div className="hidden lg:block absolute top-24 -right-2 z-20">
                    <div className="w-4 h-4 bg-blue-600 rounded-full flex items-center justify-center">
                      <ArrowRight className="w-2 h-2 text-white" />
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Siap Memulai Perjalanan Bisnis Anda?
            </h3>
            <p className="text-gray-600 mb-6">
              Bergabunglah dengan ribuan bisnis yang telah merasakan kemudahan KivaPOS
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors flex items-center justify-center">
                Mulai Gratis Sekarang
                <ArrowRight className="ml-2 w-5 h-5" />
              </button>
              
              <button className="border border-gray-300 hover:border-gray-400 text-gray-700 px-8 py-3 rounded-lg font-semibold transition-colors">
                Jadwalkan Demo
              </button>
            </div>
            
            <p className="text-sm text-gray-500 mt-4">
              Gratis 30 hari • Tidak perlu kartu kredit • Setup dalam 5 menit
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};
