"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Package,
  Users,
  Calendar,
  Target,
  BarChart3,
  Pie<PERSON><PERSON>,
} from "lucide-react";
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  Pie<PERSON><PERSON> as Recharts<PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell,
  Legend,
} from "recharts";
import {
  getSalesReportData,
  getPurchaseReportData,
  getProductReportData,
} from "@/actions/reports/reports";

interface FilterState {
  dateRange: string;
  startDate?: Date;
  endDate?: Date;
  category?: string;
  supplier?: string;
  customer?: string;
  status?: string;
}

interface ReportDashboardProps {
  filters: FilterState;
}

interface MetricCardProps {
  title: string;
  value: string;
  change: number;
  icon: React.ReactNode;
  color: string;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  change,
  icon,
  color,
}) => (
  <motion.div
    initial={{ opacity: 0, scale: 0.95 }}
    animate={{ opacity: 1, scale: 1 }}
    transition={{ duration: 0.3 }}
  >
    <Card className="relative overflow-hidden">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-slate-600 dark:text-slate-400">
          {title}
        </CardTitle>
        <div className={`p-2 rounded-lg ${color}`}>{icon}</div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-slate-900 dark:text-white mb-1">
          {value}
        </div>
        <div className="flex items-center text-sm">
          {change >= 0 ? (
            <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
          ) : (
            <TrendingDown className="h-4 w-4 text-red-500 mr-1" />
          )}
          <span className={change >= 0 ? "text-green-600" : "text-red-600"}>
            {Math.abs(change).toFixed(1)}%
          </span>
          <span className="text-slate-500 ml-1">dari periode sebelumnya</span>
        </div>
      </CardContent>
      <div
        className={`absolute bottom-0 left-0 right-0 h-1 ${color.replace("bg-", "bg-gradient-to-r from-").replace("-100", "-500 to-").replace("dark:", "")}${color.split("-")[1]}-600`}
      />
    </Card>
  </motion.div>
);

const formatCurrency = (value: number) => {
  return new Intl.NumberFormat("id-ID", {
    style: "currency",
    currency: "IDR",
    minimumFractionDigits: 0,
  }).format(value);
};

const CHART_COLORS = ["#3B82F6", "#10B981", "#F59E0B", "#EF4444", "#8B5CF6"];

export const ReportDashboard: React.FC<ReportDashboardProps> = ({
  filters,
}) => {
  const [salesData, setSalesData] = useState<any[]>([]);
  const [purchaseData, setPurchaseData] = useState<any[]>([]);
  const [productData, setProductData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [metrics, setMetrics] = useState({
    totalSales: 0,
    totalPurchases: 0,
    totalProducts: 0,
    totalCustomers: 0,
    salesChange: 0,
    purchaseChange: 0,
    productChange: 0,
    customerChange: 0,
  });

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const [salesResult, purchaseResult, productResult] = await Promise.all([
          getSalesReportData(filters.dateRange),
          getPurchaseReportData(filters.dateRange),
          getProductReportData(filters.dateRange),
        ]);

        if (salesResult.success && salesResult.data) {
          setSalesData(salesResult.data);
          const totalSales = salesResult.data.reduce(
            (sum: number, item: any) => sum + item.total,
            0
          );
          setMetrics((prev) => ({
            ...prev,
            totalSales,
            salesChange: Math.random() * 20 - 10,
          }));
        }

        if (purchaseResult.success && purchaseResult.data) {
          setPurchaseData(purchaseResult.data);
          const totalPurchases = purchaseResult.data.reduce(
            (sum: number, item: any) => sum + item.total,
            0
          );
          setMetrics((prev) => ({
            ...prev,
            totalPurchases,
            purchaseChange: Math.random() * 20 - 10,
          }));
        }

        if (productResult.success && productResult.data) {
          setProductData(productResult.data);
          setMetrics((prev) => ({
            ...prev,
            totalProducts: productResult.data.length,
            totalCustomers: Math.floor(Math.random() * 100) + 50,
            productChange: Math.random() * 20 - 10,
            customerChange: Math.random() * 20 - 10,
          }));
        }
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [filters]);

  // Prepare chart data
  const salesChartData = salesData.slice(0, 7).map((item, index) => ({
    name: `Hari ${index + 1}`,
    sales: item.total,
    purchases: purchaseData[index]?.total || 0,
  }));

  const categoryData = productData.reduce((acc: any, product: any) => {
    const category = product.category || "Lainnya";
    acc[category] = (acc[category] || 0) + product.revenue;
    return acc;
  }, {});

  const pieChartData = Object.entries(categoryData).map(
    ([name, value], index) => ({
      name,
      value: value as number,
      color: CHART_COLORS[index % CHART_COLORS.length],
    })
  );

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-slate-200 rounded w-3/4"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-slate-200 rounded w-1/2 mb-2"></div>
                <div className="h-4 bg-slate-200 rounded w-full"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Key Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Total Penjualan"
          value={formatCurrency(metrics.totalSales)}
          change={metrics.salesChange}
          icon={<DollarSign className="h-5 w-5 text-white" />}
          color="bg-blue-500 dark:bg-blue-600"
        />
        <MetricCard
          title="Total Pembelian"
          value={formatCurrency(metrics.totalPurchases)}
          change={metrics.purchaseChange}
          icon={<ShoppingCart className="h-5 w-5 text-white" />}
          color="bg-green-500 dark:bg-green-600"
        />
        <MetricCard
          title="Total Produk"
          value={metrics.totalProducts.toString()}
          change={metrics.productChange}
          icon={<Package className="h-5 w-5 text-white" />}
          color="bg-amber-500 dark:bg-amber-600"
        />
        <MetricCard
          title="Total Pelanggan"
          value={metrics.totalCustomers.toString()}
          change={metrics.customerChange}
          icon={<Users className="h-5 w-5 text-white" />}
          color="bg-purple-500 dark:bg-purple-600"
        />
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sales vs Purchases Chart */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Tren Penjualan vs Pembelian
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={salesChartData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="name" />
                    <YAxis tickFormatter={(value) => formatCurrency(value)} />
                    <Tooltip
                      formatter={(value) => formatCurrency(value as number)}
                    />
                    <Area
                      type="monotone"
                      dataKey="sales"
                      stackId="1"
                      stroke="#3B82F6"
                      fill="#3B82F6"
                      fillOpacity={0.6}
                      name="Penjualan"
                    />
                    <Area
                      type="monotone"
                      dataKey="purchases"
                      stackId="2"
                      stroke="#10B981"
                      fill="#10B981"
                      fillOpacity={0.6}
                      name="Pembelian"
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Category Distribution */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="h-5 w-5" />
                Distribusi Pendapatan per Kategori
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <RechartsPieChart>
                    <Pie
                      data={pieChartData}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      dataKey="value"
                      label={({ name, percent }) =>
                        `${name} ${(percent * 100).toFixed(0)}%`
                      }
                    >
                      {pieChartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip
                      formatter={(value) => formatCurrency(value as number)}
                    />
                    <Legend />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
};
